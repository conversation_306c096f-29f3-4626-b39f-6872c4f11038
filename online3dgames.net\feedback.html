<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback | Free Online Games Hub</title>
    <meta name="description" content="Share your feedback and suggestions for our free online games. Help us improve your gaming experience with browser games, card games, and puzzles.">
    <meta name="keywords" content="feedback, suggestions, contact, free games, online games, browser games, web games, HTML5 games">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="Feedback | Free Online Games Hub">
    <meta property="og:description" content="Share your feedback and help us improve your gaming experience!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://online3dgames.net/feedback">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://online3dgames.net/feedback">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link rel="stylesheet" href="/assets/css/modal.css">
    <style>
        .feedback-section {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 80px 20px;
            position: relative;
        }

        .feedback-content {
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid #333;
            border-radius: 20px;
            padding: 60px 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .feedback-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff6b00;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .feedback-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 25px;
            font-weight: 300;
        }

        .feedback-message {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .feedback-form {
            text-align: left;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: #ff6b00;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #333;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff6b00;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 10px rgba(255, 107, 0, 0.3);
        }

        .form-input::placeholder {
            color: #888;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .submit-btn {
            width: 100%;
            padding: 18px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: 600;
            background: #ff6b00;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 107, 0, 0.4);
        }

        .submit-btn:hover {
            background: #e55a00;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.6);
        }

        .submit-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(255, 107, 0, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .popular-games {
            margin-top: 40px;
        }

        .popular-games h3 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .game-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .game-link {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #333;
        }

        .game-link:hover {
            background: rgba(255, 107, 0, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .feedback-title { font-size: 2rem; }
            .feedback-message { font-size: 1rem; }
            .feedback-content { padding: 40px 20px; }
            .form-input { padding: 12px 15px; }
            .submit-btn { padding: 15px 25px; font-size: 1.1rem; }
        }

        /* Custom Modal Styles */
        .custom-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .custom-modal-content {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            margin: 15% auto;
            padding: 30px;
            border: 1px solid #333;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .custom-modal-title {
            color: #ff6b00;
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .custom-modal-message {
            color: #ffffff;
            font-size: 1rem;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .custom-modal-button {
            background: #ff6b00;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 107, 0, 0.4);
        }

        .custom-modal-button:hover {
            background: #e55a00;
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(255, 107, 0, 0.6);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Navigation -->
        <nav class="top-nav">
            <div class="nav-container">
                <div class="nav-left">
                    <div class="logo">
                        <span class="logo-icon">🎰</span>
                        <span class="logo-text">Online 3D Games</span>
                    </div>
                </div>
                <div class="nav-center">
                    <div class="search-bar">
                        <input type="text" placeholder="Search games..." class="search-input">
                        <button class="search-btn">🔍</button>
                    </div>
                </div>
                <div class="nav-right">
                    <button class="nav-btn" id="menuToggle">
                        <span class="hamburger"></span>
                        <span class="hamburger"></span>
                        <span class="hamburger"></span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Feedback Section -->
        <section class="feedback-section">
            <div class="feedback-content">
                <h1 class="feedback-title">Contact Support</h1>
                <p class="feedback-subtitle">Professional Gaming Support & Feedback</p>
                <p class="feedback-message">
                    Your feedback drives our innovation. Share your gaming experience, report technical issues, or suggest new features to help us deliver the ultimate online gaming platform.
                </p>

                <form class="feedback-form" id="feedbackForm">
                    <div class="form-group">
                        <label for="email" class="form-label">📧 Contact Email</label>
                        <input type="email" id="email" name="email" class="form-input" placeholder="Enter your professional email address" required>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">💭 Detailed Message</label>
                        <textarea id="message" name="message" class="form-input form-textarea" placeholder="Please provide detailed information about your inquiry, including:&#10;• Specific game or feature&#10;• Technical specifications (if reporting issues)&#10;• Detailed description of your experience&#10;• Suggestions for improvement" required></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        🚀 Submit Professional Feedback
                    </button>
                </form>

                <div class="popular-games">
                    <h3>🎮 Explore Our Premium Game Collection</h3>
                    <p style="color: rgba(255,255,255,0.7); font-size: 0.95rem; margin-bottom: 20px; text-align: center;">
                        Experience our professionally crafted games while you wait for our response
                    </p>
                    <div class="game-links">
                        <a href="/blackjack" class="game-link">♠️ Professional Blackjack</a>
                        <a href="/hearts" class="game-link">♥️ Strategic Hearts</a>
                        <a href="/sudoku-game" class="game-link">🧩 Logic Sudoku</a>
                        <a href="/tetris-game" class="game-link">🎲 Classic Tetris</a>
                        <a href="/snake-game" class="game-link">🐍 Retro Snake</a>
                        <a href="/2048" class="game-link">🧠 Brain Training 2048</a>
                        <a href="/chess" class="game-link">♔ Master Chess</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3 class="footer-title">Online 3D Games</h3>
                        <p class="footer-description">The ultimate destination for Online 3D Games. Master Blackjack, enjoy classic card games, and challenge your mind with puzzles. Play instantly in your browser with no downloads required - experience premium gaming entertainment for free!</p>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-heading">Popular Games</h4>
                        <ul class="footer-links">
                            <li><a href="/blackjack" title="Play Classic Blackjack - Master the art of 21 with professional casino strategies">Classic Blackjack</a></li>
                            <li><a href="/hearts" title="Play Hearts - Strategic trick-taking card game with intelligent AI opponents">Hearts</a></li>
                            <li><a href="/sudoku-game" title="Play Sudoku - Classic logic puzzle for brain training with multiple difficulties">Sudoku</a></li>
                            <li><a href="/tetris-game" title="Play Tetris - Arrange falling blocks to complete lines in this classic puzzle">Tetris</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-heading">Blackjack Collection</h4>
                        <ul class="footer-links">
                            <li><a href="/blackjack" title="Classic Blackjack - Master 21-point casino card game with optimal strategies">Classic Blackjack</a></li>
                            <li><a href="/blackjack-practice" title="Blackjack Practice Mode - Learn optimal strategy and improve your skills risk-free">Blackjack Practice</a></li>
                            <li><a href="/freeBetBlackjack" title="Free Bet Blackjack - Advanced variant with free double downs and splits">Free Bet Blackjack</a></li>
                            <li><a href="/pontoon-game" title="Pontoon - British Blackjack variant with unique rules and terminology">Pontoon Game</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 Online 3D Games. All rights reserved. Play Online 3D Games instantly!</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Custom Modal -->
    <div id="customModal" class="custom-modal">
        <div class="custom-modal-content">
            <div class="custom-modal-title" id="modalTitle">📢 Message</div>
            <div class="custom-modal-message" id="modalMessage">Message content</div>
            <button class="custom-modal-button" id="modalButton">OK</button>
        </div>
    </div>

    <!-- jQuery -->
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <!-- Main JS -->
    <script src="/assets/js/main.js"></script>

    <script>
        // Custom modal functions
        function showCustomModal(title, message, callback) {
            $('#modalTitle').text(title);
            $('#modalMessage').text(message);
            $('#customModal').fadeIn(300);

            // Handle button click
            $('#modalButton').off('click').on('click', function() {
                $('#customModal').fadeOut(300);
                if (callback) callback();
            });

            // Handle click outside modal
            $('#customModal').off('click').on('click', function(e) {
                if (e.target === this) {
                    $('#customModal').fadeOut(300);
                    if (callback) callback();
                }
            });
        }

        $(document).ready(function() {
            $('#feedbackForm').on('submit', function(e) {
                e.preventDefault();

                const email = $('#email').val();
                const message = $('#message').val();

                if (!email || !message) {
                    showCustomModal('⚠️ Required Fields', 'Please fill in all fields.');
                    return;
                }

                // Disable submit button to prevent double submission
                const submitBtn = $('.submit-btn');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('🔄 Sending...');

                // Send POST request to feedback API
                $.ajax({
                    url: 'https://api.online3dgames.net/feedback.php',
                    type: 'POST',
                    data: {
                        contact: email,
                        question: message
                    },
                    timeout: 10000,
                    success: function(response) {
                        showCustomModal('✅ Success', 'Thank you for your feedback! We appreciate your input and will review it carefully.', function() {
                            $('#email').val('');
                            $('#message').val('');
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Feedback submission error:', error);
                        showCustomModal('❌ Error', 'Sorry, there was an error sending your feedback. Please try again later.');
                    },
                    complete: function() {
                        // Re-enable submit button
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            $('.game-link').on('click', function() {
                const linkText = $(this).text().trim();
                console.log('Feedback Page - Game link clicked:', linkText);
            });

            $('#menuToggle').on('click', function() {
                $(this).toggleClass('active');
            });

            $('.form-input').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });
        });
    </script>
</body>
</html>
